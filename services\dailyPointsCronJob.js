const cron = require('node-cron');
const User = require('../model/user');
const { awardPointsForReceivedTransactions } = require('../controller/pointsController');

// Daily cron job to award points for received transactions
const startDailyPointsCronJob = () => {
  // Run daily at 12:00 AM (midnight)
  cron.schedule('0 0 * * *', async () => {
    console.log('🚀 Starting daily points award process...');
    console.log('Time:', new Date().toISOString());
    
    try {
      // Get all users who have Solana wallet addresses
      const usersWithSolanaWallets = await User.find({
        'walletAddress.solanaWalletAddress': { $exists: true, $ne: null, $ne: '' }
      });

      console.log(`📊 Found ${usersWithSolanaWallets.length} users with Solana wallets`);

      let processedCount = 0;
      let pointsAwardedCount = 0;
      let totalPointsAwarded = 0;
      let errorCount = 0;

      // Process each user one by one
      for (const user of usersWithSolanaWallets) {
        try {
          console.log(`👤 Processing user: ${user.walletAddress.solanaWalletAddress}`);
          
          // Create mock request and response objects
          const mockReq = {
            body: {
              walletAddress: user.walletAddress.solanaWalletAddress
            }
          };

          let responseData = null;
          let statusCode = null;
          
          const mockRes = {
            status: (code) => ({
              json: (data) => {
                statusCode = code;
                responseData = data;
                return { statusCode: code, data };
              }
            })
          };

          // Call the points award function
          await awardPointsForReceivedTransactions(mockReq, mockRes);
          
          processedCount++;

          // Log the result
          if (statusCode === 200 && responseData) {
            if (responseData.pointsAwarded > 0) {
              pointsAwardedCount++;
              totalPointsAwarded += responseData.pointsAwarded;
              console.log(`✅ Awarded ${responseData.pointsAwarded} points`);
            } else {
              console.log(`ℹ️  No points awarded - Total: $${responseData.totalReceivedAmount || 0}`);
            }
          }

          // Add delay to avoid overwhelming APIs
          await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay

        } catch (userError) {
          errorCount++;
          console.error(`❌ Error processing user ${user.walletAddress.solanaWalletAddress}:`, userError.message);
        }
      }

      // Final summary
      console.log('\n📈 DAILY POINTS SUMMARY:');
      console.log(`👥 Users processed: ${processedCount}`);
      console.log(`🎯 Users got points: ${pointsAwardedCount}`);
      console.log(`🏆 Total points awarded: ${totalPointsAwarded}`);
      console.log(`❌ Errors: ${errorCount}`);
      console.log(`⏰ Completed: ${new Date().toISOString()}`);

    } catch (error) {
      console.error('💥 Fatal error in daily points cron job:', error);
    }
  });

  console.log('⏰ Daily points cron job scheduled - runs every day at midnight');
};

module.exports = startDailyPointsCronJob;
