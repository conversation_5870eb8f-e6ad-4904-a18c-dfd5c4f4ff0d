const asyncHandler = require("express-async-handler");
const User = require("../model/user");
const Points = require("../model/points");
const { paginate } = require("../utils");
const axios = require("axios");

// Function to get current SOL price in USD
const getSolPriceUSD = async () => {
  try {
    // Try CoinGecko API first
    const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd', {
      timeout: 5000
    });
    return response.data.solana.usd;
  } catch (error) {
    console.log('Failed to fetch SOL price from CoinGecko, using fallback price');
    // Fallback to estimated price if API fails
    return 100; // $100 per SOL as fallback
  }
};

// Function to get token price in USD using Moralis API
const getTokenPriceUSD = async (symbol, contractAddress) => {
  try {
    // For SOL, use a known contract address or handle separately
    if (symbol === 'SOL') {
      return await getSolPriceUSD();
    }

    // For other tokens, use Moralis token price API
    if (contractAddress) {
      const response = await axios.get(`https://solana-gateway.moralis.io/token/${contractAddress}/price`, {
        headers: {
          'X-API-Key': process.env.MORALIS_API_KEY,
          'accept': 'application/json'
        },
        timeout: 10000
      });

      return response.data?.usdPrice || 0;
    }

    // Fallback: Try to get price by symbol using Moralis
    // Note: This might not work for all tokens, but worth trying
    const commonTokens = {
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'BONK': 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      'WIF': 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
      'JUP': 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN',
      'ORCA': 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
      'RAY': '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R'
    };

    const tokenAddress = commonTokens[symbol.toUpperCase()];
    if (tokenAddress) {
      const response = await axios.get(`https://solana-gateway.moralis.io/token/${tokenAddress}/price`, {
        headers: {
          'X-API-Key': process.env.MORALIS_API_KEY,
          'accept': 'application/json'
        },
        timeout: 10000
      });

      return response.data?.usdPrice || 0;
    }

    return 0; // No price found
  } catch (error) {
    console.log(`Failed to fetch price for ${symbol} using Moralis:`, error.message);

    // Fallback to CoinGecko for major tokens
    try {
      const coinGeckoMappings = {
        'BTC': 'bitcoin',
        'ETH': 'ethereum',
        'USDC': 'usd-coin',
        'USDT': 'tether'
      };

      const coinId = coinGeckoMappings[symbol.toUpperCase()];
      if (coinId) {
        const response = await axios.get(`https://api.coingecko.com/api/v3/simple/price?ids=${coinId}&vs_currencies=usd`, {
          timeout: 5000
        });
        return response.data[coinId]?.usd || 0;
      }
    } catch (fallbackError) {
      console.log(`Fallback CoinGecko also failed for ${symbol}`);
    }

    return 0;
  }
};

const getAllPoints = asyncHandler(async (req, res) => {
  const points = await Points.find();
  res.status(200).json(points);
});

const getAllPointsHistory = asyncHandler(async (req, res) => {
  const { page } = req.query;
  const { walletAddress } = req.params;

  if (!walletAddress) {
    return res.status(400).json({ message: "Wallet address is required" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  });

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const pointsHistory = await Points.find({
    walletAddress:
      user.walletAddress.ethWalletAddress ||
      user.walletAddress.solanaWalletAddress,
  }).sort({ createdAt: -1 });

  const { paginatedItems, totalPages, currentPage, totalItems } = paginate(
    pointsHistory,
    15,
    page
  );

  res
    .status(200)
    .json({
      pointsHistory: paginatedItems,
      totalPages,
      currentPage,
      totalItems,
    });
});

const getWalletAddressPoints = asyncHandler(async (req, res) => {
  const { walletAddress } = req.params;

  if (!walletAddress) {
    return res.status(400).json({ message: "Wallet address is required" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  });

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const totalPoints = user.totalPoints;

  res.status(200).json({ totalPoints });
});

const awardPointsForDCAOrderHistory = asyncHandler(async (req, res) => {
  const { walletAddress } = req.body;
  if (!walletAddress) {
    return res.status(400).json({ message: "walletAddress is required" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.solanaWalletAddress": walletAddress },
      { "walletAddress.ethWalletAddress": walletAddress },
    ],
  });

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const solanaAddress = user.walletAddress.solanaWalletAddress;
  if (!solanaAddress) {
    return res
      .status(400)
      .json({ message: "User does not have a Solana wallet address" });
  }

  const url = `https://lite-api.jup.ag/recurring/v1/getRecurringOrders?user=${solanaAddress}&orderStatus=history&recurringType=all&includeFailedTx=false`;
  let orders = [];
  try {
    const response = await axios.get(url, {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      timeout: 10000,
    });

    orders = response.data.all || [];
  } catch (err) {
    return res
      .status(502)
      .json({
        message: "Failed to fetch order history from Jupiter API",
        error: err.message,
      });
  }

  const awardedPoints = await Points.find({
    userId: user._id,
    pointsType: "dca",
    "metadata.txIds": { $exists: true, $ne: [] }
  });
  const alreadyAwardedTxIds = new Set();
  for (const doc of awardedPoints) {
    if (Array.isArray(doc.metadata.txIds)) {
      doc.metadata.txIds.forEach(txId => alreadyAwardedTxIds.add(txId));
    }
  }

  let totalOrderAmount = 0;
  let newTxIds = [];
  let newTradeCount = 0;
  for (const order of orders) {
    if (!order.trades || order.trades.length === 0) continue;
    for (const trade of order.trades) {
      if (!trade.txId || alreadyAwardedTxIds.has(trade.txId)) continue;
      const amount = Number(trade.inputAmount) || 0;
      totalOrderAmount += amount;
      newTxIds.push(trade.txId);
      newTradeCount++;
    }
  }

  // Calculate points based on $100 increments
  const currentTotal = (user.lastPointOn || 0) + totalOrderAmount;
  const pointsToAward = Math.floor(currentTotal / 100) * 1000 - Math.floor((user.lastPointOn || 0) / 100) * 1000;

  if (pointsToAward === 0) {
    return res
      .status(200)
      .json({
        message: "No new points to award. Need $100 deposit increment.",
        pointsAwarded: 0,
      });
  }

  user.totalPoints = (user.totalPoints || 0) + pointsToAward;
  user.lastPointOn = currentTotal;
  await user.save();

  await Points.create({
    userId: user._id,
    walletAddress: solanaAddress,
    pointsType: "dca",
    points: pointsToAward,
    description: `Awarded ${pointsToAward} points for DCA deposits (1000 points per $100)`,
    metadata: {
      txIds: newTxIds,
      tradeCount: newTradeCount,
      totalOrderAmount,
      lastPointOn: user.lastPointOn,
      source: "jupiter-dca-history",
    },
  });

  res.status(200).json({
    message: "Points awarded for DCA deposits",
    pointsAwarded: pointsToAward,
    tradeCount: newTradeCount,
    totalOrderAmount,
    totalPoints: user.totalPoints,
    lastPointOn: user.lastPointOn,
    awardedTxIds: newTxIds,
  });
});

const awardPointsForReceivedTransactions = asyncHandler(async (req, res) => {
  const { walletAddress } = req.body;
  if (!walletAddress) {
    return res.status(400).json({ message: "walletAddress is required" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.solanaWalletAddress": walletAddress },
      { "walletAddress.ethWalletAddress": walletAddress },
    ],
  });

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const solanaAddress = user.walletAddress.solanaWalletAddress;
  if (!solanaAddress) {
    return res
      .status(400)
      .json({ message: "User does not have a Solana wallet address" });
  }

  // Fetch transactions from Bird API
  const birdApiUrl = `https://public-api.birdeye.so/v1/wallet/tx_list`;
  let transactions = [];

  try {
    const response = await axios.get(birdApiUrl, {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "X-API-KEY": process.env.BIRDEYE_API_KEY || "",
        "x-chain": "solana",
      },
      params: {
        wallet: solanaAddress,
        limit: 100,
      },
      timeout: 10000,
    });

    // Extract Solana transactions from the response
    transactions = response.data?.data?.solana || [];
  } catch (err) {
    return res
      .status(502)
      .json({
        message: "Failed to fetch transactions from Bird API",
        error: err.message,
      });
  }
  console.log("=== BIRD API RESPONSE DEBUG ===");
  console.log("Total transactions found:", transactions.length);

  // Filter and log received transactions
  const receivedTransactions = transactions.filter(tx => tx.mainAction === "received");
  console.log("Received transactions found:", receivedTransactions.length);

  // Log first received transaction in detail
  if (receivedTransactions.length > 0) {
    const firstTx = receivedTransactions[0];
    console.log("\n=== FIRST RECEIVED TRANSACTION DETAILS ===");
    console.log("Hash:", firstTx.txHash);
    console.log("MainAction:", firstTx.mainAction);
    console.log("ValueUSD:", firstTx.valueUSD);
    console.log("BalanceChange (stringified):", JSON.stringify(firstTx.balanceChange, null, 2));

    // Check each balance change
    if (firstTx.balanceChange && Array.isArray(firstTx.balanceChange)) {
      firstTx.balanceChange.forEach((change, idx) => {
        console.log(`Balance Change ${idx + 1}:`, {
          symbol: change.symbol,
          amount: change.amount,
          decimals: change.decimals,
          valueUSD: change.valueUSD,
          address: change.address
        });
      });
    }
  }

  // Get already awarded transaction signatures to avoid duplicate points
  const awardedPoints = await Points.find({
    userId: user._id,
    pointsType: "received",
    "metadata.txSignatures": { $exists: true, $ne: [] }
  });

  const alreadyAwardedTxSignatures = new Set();
  for (const doc of awardedPoints) {
    if (Array.isArray(doc.metadata.txSignatures)) {
      doc.metadata.txSignatures.forEach(sig => alreadyAwardedTxSignatures.add(sig));
    }
  }

  // Filter for received transactions only and calculate total received amount
  let totalReceivedAmount = 0;
  let newTxSignatures = [];
  let newReceivedCount = 0;

  for (const tx of transactions) {
    // Skip if already awarded points for this transaction
    if (alreadyAwardedTxSignatures.has(tx.txHash)) {
      console.log(`Skipping already awarded transaction: ${tx.txHash}`);
      continue;
    }

    // Check if this is a received transaction using mainAction field
    const isReceived = tx.mainAction === "received";

    if (isReceived) {
      console.log(`\n=== Processing Received Transaction ===`);
      console.log(`Hash: ${tx.txHash}`);

      // Get USD value directly from transaction if available
      let txAmountUSD = 0;

      // Check if transaction has direct USD value
      if (tx.valueUSD && tx.valueUSD > 0) {
        txAmountUSD = tx.valueUSD;
        console.log(`Direct USD value found: $${txAmountUSD}`);
      }
      // Fallback: calculate from balance changes if no direct USD value
      else if (tx.balanceChange && Array.isArray(tx.balanceChange)) {
        console.log(`Checking balance changes...`);
        for (const change of tx.balanceChange) {
          console.log(`Balance change:`, {
            symbol: change.symbol,
            amount: change.amount,
            decimals: change.decimals,
            valueUSD: change.valueUSD
          });

          if (change.amount > 0) { // Positive amount means received
            // Check if the token has USD value information
            if (change.valueUSD && change.valueUSD > 0) {
              txAmountUSD += change.valueUSD;
              console.log(`Added USD value from balance change: $${change.valueUSD}`);
            }
            // If no USD value, try to estimate for major tokens
            else if (change.symbol === "USDC" || change.symbol === "USDT") {
              // For stablecoins, amount is approximately equal to USD
              const tokenAmount = change.amount / Math.pow(10, change.decimals || 6);
              txAmountUSD += tokenAmount;
              console.log(`Added stablecoin value: $${tokenAmount}`);
            }
            // For SOL, convert to USD using current price
            else if (change.symbol === "SOL") {
              const solAmount = change.amount / Math.pow(10, change.decimals || 9);
              // Fetch SOL price from CoinGecko or use estimated price
              const solPriceUSD = await getSolPriceUSD();
              const solValueUSD = solAmount * solPriceUSD;
              txAmountUSD += solValueUSD;
              console.log(`Added SOL value: ${solAmount} SOL × $${solPriceUSD} = $${solValueUSD}`);
            }
            // For other major tokens, try to get price from CoinGecko
            else {
              try {
                const tokenAmount = change.amount / Math.pow(10, change.decimals || 9);
                const tokenPriceUSD = await getTokenPriceUSD(change.symbol, change.address);

                if (tokenPriceUSD > 0) {
                  const tokenValueUSD = tokenAmount * tokenPriceUSD;
                  txAmountUSD += tokenValueUSD;
                  console.log(`Added ${change.symbol} value: ${tokenAmount} × $${tokenPriceUSD} = $${tokenValueUSD}`);
                } else {
                  // Fallback: For very small amounts, assume minimal value to avoid completely ignoring
                  if (tokenAmount > 0) {
                    console.log(`No USD price found for ${change.symbol}, but token amount exists: ${tokenAmount}`);
                    // Don't add any value, but log for debugging
                  }
                }
              } catch (error) {
                console.log(`Error fetching price for ${change.symbol}:`, error.message);
              }
            }
          } else {
            console.log(`Negative amount, skipping: ${change.amount}`);
          }
        }
      }

      console.log(`Total USD amount for this transaction: $${txAmountUSD}`);

      if (txAmountUSD > 0) {
        totalReceivedAmount += txAmountUSD;
        newTxSignatures.push(tx.txHash);
        newReceivedCount++;
        console.log(`Added to total. Running total: $${totalReceivedAmount}`);
      } else {
        console.log(`No USD value found, skipping transaction`);
      }
    }
  }

  // Calculate points based on $100 increments (1000 points per $100)
  // Track cumulative received amount to handle partial amounts correctly
  const currentReceivedTotal = (user.lastReceivedPointOn || 0) + totalReceivedAmount;
  const pointsToAward = Math.floor(currentReceivedTotal / 100) * 1000 - Math.floor((user.lastReceivedPointOn || 0) / 100) * 1000;
  // const pointsToAward =10;

  if (pointsToAward === 0) {
    return res
      .status(200)
      .json({
        message: "No new points to award. Need $100 received increment.",
        pointsAwarded: 0,
        totalReceivedAmount,
        currentReceivedTotal,
        userTotalPoints: user.totalPoints,
        lastReceivedPointOn: user.lastReceivedPointOn,
        user: user,
      });
  }

  // Update user points and tracking
  user.totalPoints = (user.totalPoints || 0) + pointsToAward;
  user.lastReceivedPointOn = currentReceivedTotal;
  await user.save();

  // Create points record
  await Points.create({
    userId: user._id,
    walletAddress: solanaAddress,
    pointsType: "received",
    points: pointsToAward,
    description: `Awarded ${pointsToAward} points for received transactions (1000 points per $100)`,
    metadata: {
      txSignatures: newTxSignatures,
      receivedCount: newReceivedCount,
      totalReceivedAmount,
      lastReceivedPointOn: user.lastReceivedPointOn,
      source: "birdeye-received-transactions",
    },
  });

  res.status(200).json({
    message: "Points awarded for received transactions",
    pointsAwarded: pointsToAward,
    receivedCount: newReceivedCount,
    totalReceivedAmount,
    totalPoints: user.totalPoints,
    lastReceivedPointOn: user.lastReceivedPointOn,
    awardedTxSignatures: newTxSignatures,
    
  });
});

module.exports = {
  getAllPoints,
  getAllPointsHistory,
  getWalletAddressPoints,
  awardPointsForDCAOrderHistory,
  awardPointsForReceivedTransactions,
};
