const mongoose = require("mongoose");

const taskSchema = new mongoose.Schema(
  {
    title: {
      type: String,
    },
    requirements: {
      type: Number,
    },
    description: {
      type: String,
    },
    points: {
      type: Number,
      default: 10,
    },
    type: {
      type: String,
    },
    image: {
      type: String,
      default: "",
    },
    url: {
      type: String,
      default: "",
    },
    category: {
      type: String,
    },
    status: {
      type: String,
      enum: ["live", "ended"],
      default: "live",
    },
    completedBy: [
      {
        walletAddress: {
          type: String,
        },
        completedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
  },
  { timestamps: true }
);

const Task = mongoose.model("Task", taskSchema);
module.exports = Task;
