const mongoose = require("mongoose");
const tokenSwapSchema = new mongoose.Schema(
  {
    walletAddress: {
      type: String,
    },
    inputToken: {
      type: String,
    },
    outputToken: {
      type: String,
    },
    inputAmount: {
      type: Number,
    },
    outputAmount: {
      type: Number,
      default: 0,
    },
    transactionHash: {
      type: String,
    },
  },
  { timestamps: true }
);

const TokenSwap = mongoose.model("TokenSwapData", tokenSwapSchema);
module.exports = TokenSwap;
