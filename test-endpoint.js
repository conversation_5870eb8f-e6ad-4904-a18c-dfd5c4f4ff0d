const axios = require("axios");

// Test Bird API directly to see USD values
async function testBirdAPIForUSD() {
  const testWallet = "********************************************";
  const birdApiUrl = `https://public-api.birdeye.so/v1/wallet/tx_list`;

  try {
    console.log("Testing Bird API for USD values...");

    const response = await axios.get(birdApiUrl, {
      headers: {
        "X-API-KEY": "4dbff0971767481191c6d12c5679dcd9", // From .env
        "x-chain": "solana",
      },
      params: {
        wallet: testWallet,
        limit: 5,
      },
    });

    const transactions = response.data?.data?.solana || [];
    console.log(`Found ${transactions.length} transactions`);

    transactions.forEach((tx, index) => {
      if (tx.mainAction === "received") {
        console.log(`\nReceived Transaction ${index + 1}:`);
        console.log(`  Hash: ${tx.txHash}`);
        console.log(`  Direct USD Value: ${tx.valueUSD || 'Not available'}`);
        console.log(`  Balance Changes:`, tx.balanceChange?.map(change => ({
          symbol: change.symbol,
          amount: change.amount,
          valueUSD: change.valueUSD || 'Not available'
        })));
      }
    });

  } catch (error) {
    console.error("Error:", error.message);
  }
}

async function testReceivedTransactionPoints() {
  const testWallet = "********************************************";
  const apiUrl = "http://localhost:7000/points/award-received-transaction-points";

  try {
    console.log("\n=== Testing Points Endpoint ===");
    console.log("Wallet:", testWallet);

    const response = await axios.post(apiUrl, {
      walletAddress: testWallet
    }, {
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 30000,
    });

    console.log("\n✅ Success!");
    console.log("Response:", JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log("\n❌ Error:");
    console.log("Message:", error.message);

    if (error.response) {
      console.log("Status:", error.response.status);
      console.log("Response:", JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run both tests
async function runTests() {
  await testBirdAPIForUSD();
  await testReceivedTransactionPoints();
}

runTests();
