const axios = require("axios");

async function testReceivedTransactionPoints() {
  const testWallet = "********************************************"; // Example wallet from Bird API docs
  const apiUrl = "http://localhost:7000/points/award-received-transaction-points";
  
  try {
    console.log("Testing received transaction points endpoint...");
    console.log("Wallet:", testWallet);
    console.log("URL:", apiUrl);
    
    const response = await axios.post(apiUrl, {
      walletAddress: testWallet
    }, {
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 30000,
    });

    console.log("\n✅ Success!");
    console.log("Status:", response.status);
    console.log("Response:", JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log("\n❌ Error:");
    console.log("Message:", error.message);
    
    if (error.response) {
      console.log("Status:", error.response.status);
      console.log("Response:", JSON.stringify(error.response.data, null, 2));
    }
  }
}

testReceivedTransactionPoints();
