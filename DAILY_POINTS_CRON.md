# Daily Points Cron Job

## Overview

Automatic daily process that awards points to all users based on their received Solana transactions.

## How It Works

1. **Runs Daily at Midnight** (12:00 AM)
2. **Finds All Users** with Solana wallet addresses
3. **Processes Each User** one by one
4. **Awards Points** based on received transactions (1000 points per $100 USD)
5. **Logs Results** for monitoring

## Features

- ⏰ **Automatic Scheduling**: Runs every day at midnight
- 👥 **Bulk Processing**: Handles all users automatically
- 🔄 **Rate Limiting**: 1-second delay between users
- 📊 **Detailed Logging**: Complete process tracking
- 🛡️ **Error Handling**: Continues processing even if individual users fail
- 📈 **Summary Reports**: Statistics after completion

## Manual Testing

For immediate testing, use the manual endpoint:

```bash
POST /points/run-daily-points-process
```

**Response:**
```json
{
  "message": "Daily points process completed",
  "summary": {
    "totalUsersProcessed": 25,
    "usersWhoReceivedPoints": 3,
    "totalPointsAwarded": 3000
  },
  "results": [
    {
      "wallet": "ABC123...",
      "pointsAwarded": 1000,
      "totalReceived": 150.50
    }
  ]
}
```

## Console Output

```
🚀 Starting daily points award process...
📊 Found 25 users with Solana wallets

👤 Processing user: ABC123...
✅ Awarded 1000 points

👤 Processing user: DEF456...
ℹ️  No points awarded - Total: $45.30

📈 DAILY POINTS SUMMARY:
👥 Users processed: 25
🎯 Users got points: 3
🏆 Total points awarded: 3000
❌ Errors: 0
⏰ Completed: 2025-01-24T00:01:30.000Z
```

## Configuration

- **Schedule**: `0 0 * * *` (midnight daily)
- **Delay**: 1 second between users
- **Timeout**: 10 seconds per API call
- **Error Handling**: Per-user error isolation

## Files

- `services/dailyPointsCronJob.js` - Main cron job logic
- `routes/pointsRoutes.js` - Manual test endpoint
- `index.js` - Cron job initialization

## Monitoring

The cron job provides comprehensive logging:

1. **Start Time**: When process begins
2. **User Count**: How many users found
3. **Individual Results**: Per-user processing status
4. **Final Summary**: Complete statistics
5. **Error Tracking**: Any failures encountered

## Points Logic

- **$100 USD = 1000 points**
- **Cumulative tracking**: Partial amounts carry over
- **No duplicates**: Same transaction won't award points twice
- **Real-time prices**: Bird API for accurate USD conversion
