# Bird API Integration for Solana Wallet Transaction Points

## Overview

This integration uses the Bird API (birdeye.so) to track received transactions in Solana wallets and award points based on the USD value of received amounts.

## Features

- **Automatic Point Calculation**: Awards 1000 points for every $100 received
- **Cumulative Tracking**: Tracks partial amounts (e.g., $50 + $50 = $100 = 1000 points)
- **Duplicate Prevention**: Prevents awarding points for the same transaction multiple times
- **Direct USD Value Tracking**: Uses USD values directly from Bird API for accurate point calculation

## API Endpoint

### Award Points for Received Transactions

**POST** `/points/award-received-transaction-points`

**Request Body:**
```json
{
  "walletAddress": "SOLANA_WALLET_ADDRESS"
}
```

**Response:**
```json
{
  "message": "Points awarded for received transactions",
  "pointsAwarded": 1000,
  "receivedCount": 3,
  "totalReceivedAmount": 120.50,
  "totalPoints": 5000,
  "lastReceivedPointOn": 220.50,
  "awardedTxSignatures": ["tx1", "tx2", "tx3"]
}
```

## Point Calculation Logic

1. **Fetch Transactions**: Retrieves up to 100 recent transactions from Bird API
2. **Filter Received**: Only processes transactions with `mainAction: "received"`
3. **Extract USD Value**: Uses direct USD values from Bird API response
   - Primary: `tx.valueUSD` (direct transaction USD value)
   - Fallback: `change.valueUSD` from balance changes
   - Stablecoin Support: Direct conversion for USDC/USDT
4. **Cumulative Tracking**: Adds new received amounts to previous total
5. **Award Points**: Awards 1000 points for each complete $100 increment

### Example Scenarios

#### Scenario 1: First-time user receives $150
- Previous total: $0
- New received: $150
- Points awarded: 1000 (for first $100)
- Remaining $50 tracked for future

#### Scenario 2: User with $50 tracked receives another $75
- Previous total: $50
- New received: $75
- Combined total: $125
- Points awarded: 1000 (for reaching $100)
- Remaining $25 tracked for future

#### Scenario 3: User receives $250 in one transaction
- Previous total: $0
- New received: $250
- Points awarded: 2000 (for $200 worth)
- Remaining $50 tracked for future

## Database Schema Updates

### User Model
Added new field:
```javascript
lastReceivedPointOn: {
  type: Number,
  default: 0,
}
```

### Points Model
Added new point type:
```javascript
pointsType: {
  type: String,
  enum: ["refer", "login", "task", "bonus", "buy", "sell", "dca", "received"],
  required: true,
}
```

## Configuration

### Environment Variables
- `BIRDEYE_API_KEY`: Your Bird API key (already configured in .env)

### USD Value Sources
The system prioritizes USD values in this order:

1. **Direct Transaction USD Value**: `tx.valueUSD` from Bird API
2. **Token-specific USD Values**: `change.valueUSD` from balance changes
3. **Stablecoin Conversion**: Direct 1:1 conversion for USDC/USDT
4. **Fallback**: Skip tokens without USD value information

## Usage Examples

### Using cURL
```bash
curl -X POST http://localhost:7000/points/award-received-transaction-points \
  -H "Content-Type: application/json" \
  -d '{"walletAddress": "YOUR_SOLANA_WALLET_ADDRESS"}'
```

### Using JavaScript/Frontend
```javascript
const response = await fetch('/points/award-received-transaction-points', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    walletAddress: 'YOUR_SOLANA_WALLET_ADDRESS'
  })
});

const result = await response.json();
console.log('Points awarded:', result.pointsAwarded);
```

## Error Handling

The API handles various error scenarios:

- **Missing wallet address**: Returns 400 error
- **User not found**: Returns 404 error
- **No Solana wallet**: Returns 400 error
- **Bird API failure**: Returns 502 error with details
- **No new points to award**: Returns 200 with pointsAwarded: 0

## Security Considerations

1. **API Rate Limiting**: Bird API has rate limits - consider implementing caching
2. **Duplicate Prevention**: System prevents double-awarding points for same transaction
3. **Validation**: Validates wallet addresses and user existence

## Future Enhancements

1. **Real-time Price Integration**: Replace fixed SOL price with live rates
2. **Multi-token Support**: Extend to support other SPL tokens
3. **Webhook Integration**: Real-time transaction monitoring
4. **Admin Dashboard**: View and manage point awards
5. **Batch Processing**: Process multiple wallets efficiently

## Troubleshooting

### Common Issues

1. **No points awarded**: Check if transactions are actually "received" type
2. **API timeout**: Bird API may be slow - increase timeout if needed
3. **Wrong amounts**: Verify SOL price conversion logic
4. **Duplicate points**: Check transaction signature tracking

### Debug Mode
Add logging to see transaction details:
```javascript
console.log('Processing transaction:', tx.txHash, tx.mainAction, tx.balanceChange);
```
