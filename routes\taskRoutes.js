const express = require("express");
const router = express.Router();
const taskController = require("../controller/taskController");
const Validator = require("../middleware/validateTokenHandler");
const upload = require("../middleware/multer");

router.post(
  "/createTask",
  upload.single("image"),
  Validator,
  taskController.addTask
);
router.get("/getAllTasks", taskController.getAllTasks);
router.get("/getTask/:taskId", taskController.getTaskById);
router.post("/performTask/:walletAddress/:taskId", taskController.performTask);
router.delete("/deleteTask/:taskId", Validator, taskController.deleteTask);
router.put(
  "/updateTask/:taskId",
  upload.single("image"),
  Validator,
  taskController.updateTask
);
router.get("/getCompletedTasksByUser/:walletAddress", taskController.getCompletedTasksByUser);
router.get("/getAllCategories", taskController.getAllCategories);
router.get("/getAllTasksbyWallet", taskController.getAllTasksbyWallet);
router.get("/getCategory", taskController.getCategory);
router.post("/createCategory", taskController.createCategory);
router.get('/getSocialTasks/:walletAddress', taskController.getSocialTasksByWallet);

module.exports = router;
