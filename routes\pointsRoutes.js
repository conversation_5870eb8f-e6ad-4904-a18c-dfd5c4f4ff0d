const express = require("express");
const router = express.Router();
const pointsController = require("../controller/pointsController");

router.get("/allPoints", pointsController.getAllPoints);
router.get("/history/:walletAddress", pointsController.getAllPointsHistory);
router.get(
  "/walletPoints/:walletAddress",
  pointsController.getWalletAddressPoints
);
router.post(
  "/award-dca-history-points",
  pointsController.awardPointsForDCAOrderHistory
);
router.post(
  "/award-received-transaction-points",
  pointsController.awardPointsForReceivedTransactions
);

// Manual trigger for daily points process (for testing)
router.post("/run-daily-points-process", async (req, res) => {
  try {
    const User = require("../model/user");

    console.log('🚀 Manual daily points process started...');

    // Get all users with Solana wallets
    const usersWithSolanaWallets = await User.find({
      'walletAddress.solanaWalletAddress': { $exists: true, $ne: null, $ne: '' }
    });

    console.log(`📊 Found ${usersWithSolanaWallets.length} users with Solana wallets`);

    let processedCount = 0;
    let pointsAwardedCount = 0;
    let totalPointsAwarded = 0;
    let results = [];

    // Process each user
    for (const user of usersWithSolanaWallets) {
      try {
        console.log(`👤 Processing: ${user.walletAddress.solanaWalletAddress}`);

        const mockReq = {
          body: { walletAddress: user.walletAddress.solanaWalletAddress }
        };

        let responseData = null;
        const mockRes = {
          status: (code) => ({
            json: (data) => {
              responseData = data;
              return { statusCode: code, data };
            }
          })
        };

        await pointsController.awardPointsForReceivedTransactions(mockReq, mockRes);

        processedCount++;

        if (responseData && responseData.pointsAwarded > 0) {
          pointsAwardedCount++;
          totalPointsAwarded += responseData.pointsAwarded;
          console.log(`✅ Awarded ${responseData.pointsAwarded} points`);
        } else {
          console.log(`ℹ️  No points - Total: $${responseData?.totalReceivedAmount || 0}`);
        }

        results.push({
          wallet: user.walletAddress.solanaWalletAddress,
          pointsAwarded: responseData?.pointsAwarded || 0,
          totalReceived: responseData?.totalReceivedAmount || 0
        });

        // Small delay to avoid API rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (userError) {
        console.error(`❌ Error processing user:`, userError.message);
        results.push({
          wallet: user.walletAddress.solanaWalletAddress,
          error: userError.message
        });
      }
    }

    console.log('\n📈 MANUAL PROCESS SUMMARY:');
    console.log(`👥 Users processed: ${processedCount}`);
    console.log(`🎯 Users got points: ${pointsAwardedCount}`);
    console.log(`🏆 Total points awarded: ${totalPointsAwarded}`);

    res.status(200).json({
      message: "Daily points process completed",
      summary: {
        totalUsersProcessed: processedCount,
        usersWhoReceivedPoints: pointsAwardedCount,
        totalPointsAwarded: totalPointsAwarded
      },
      results: results
    });

  } catch (error) {
    console.error('💥 Error in manual daily points process:', error);
    res.status(500).json({
      message: "Error running daily points process",
      error: error.message
    });
  }
});

module.exports = router;
