const mongoose = require("mongoose");

const registerSchema = new mongoose.Schema(
  {
    walletAddress: {
      solanaWalletAddress: { type: String, default: null },
      ethWalletAddress: { type: String, default: null },
    },

    totalPoints: {
      type: Number,
      default: 0,
    },
    lastPointOn: {
      type: Number,
      default: 0,
    },
    lastReceivedPointOn: {
      type: Number,
      default: 0,
    },
    lastLoginDate: {
      type: Date,
    },
    rank: {
      type: String,
      enum: ["bronze", "silver", "gold"],
      default: "bronze",
    },
    referralCode: {
      type: String,
      unique: true,
    },
    referredBy: {
      type: String,
      default: null,
    },
    referralStatus: {
      type: String,
      enum: ["pending", "qualified", null],
      default: null,
    },
    completedTasks: [
      {
        taskId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Task",
        },
        walletAddress: {
          type: String,
        },
        taskPoints: {
          type: Number,
        },
        taskType: {
          type: String,
        },
        completedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    // Track which ranks have been redeemed
    redeemedRanks: [
      {
        rank: {
          type: String,
          enum: ["bronze", "silver", "gold"],
        },
        redeemedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
  },
  { timestamps: true }
);

module.exports = mongoose.model("User", registerSchema);
