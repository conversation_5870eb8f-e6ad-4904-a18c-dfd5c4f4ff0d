const asyncHandler = require("express-async-handler");
const User = require("../model/user");
const Admin = require("../model/admin");
const Task = require("../model/task");
const Points = require("../model/points");
const TaskCategory = require("../model/taskCategory");
const { uploadFile } = require("../config/GCS");
const { paginate } = require("../utils");

const addTask = asyncHandler(async (req, res) => {
  const adminId = req.user.id;
  const { title, url, category, requirements, points, type, description } =
    req.body;

  if (!adminId) {
    return res
      .status(401)
      .json({ message: "Unauthorized. Admin token missing" });
  }

  const admin = await Admin.findById(adminId);
  if (!admin) {
    return res.status(404).json({ message: "Admin not found" });
  }

  if (!title || !category || !points) {
    return res.status(400).json({ message: "Title and Category are required" });
  }

  let imageUrl = null;

  if (req.file) {
    try {
      const uploadResult = await uploadFile(req.file, "tasks");

      if (!uploadResult.success) {
        return res.status(400).json({
          message: "Image upload failed",
          error: uploadResult.error,
          details: uploadResult.details,
        });
      }

      imageUrl = uploadResult.data.publicUrl || uploadResult.data.signedUrl;
    } catch (uploadError) {
      console.error("Image upload failed:", uploadError.message);
      return res.status(500).json({
        message: "Image upload failed",
        error: "UPLOAD_ERROR",
        details: uploadError.message,
      });
    }
  }

  const task = new Task({
    title,
    description,
    points,
    url,
    category,
    image: imageUrl,
    requirements,
    type,
  });

  await task.save();

  res.status(201).json({
    message: "Task added successfully",
    task,
  });
});

const getAllTasks = asyncHandler(async (req, res) => {
  const { category, walletAddress, page } = req.query;

  let filter = {};
  if (category) {
    filter.category = category;
  }

  const tasks = await Task.find(filter);

  if (!tasks || tasks.length === 0) {
    return res.status(200).json({ message: "No tasks found" });
  }

  const updatedTasks = tasks.map((task) => {
    const isComplete = walletAddress
      ? task.completedBy.some((entry) => {
          let parsed = {};

          try {
            let fixed = entry.walletAddress
              .replace(/([a-zA-Z0-9]+):/g, '"$1":')
              .replace(/'/g, '"');

            parsed = JSON.parse(fixed);
          } catch (err) {
            console.warn("❌ Invalid walletAddress JSON:", err.message);
            return false;
          }

          const lowerInput = walletAddress.toLowerCase();
          return (
            (parsed.ethWalletAddress &&
              parsed.ethWalletAddress.toLowerCase() === lowerInput) ||
            (parsed.solanaWalletAddress &&
              parsed.solanaWalletAddress.toLowerCase() === lowerInput)
          );
        })
      : false;

    return {
      ...task.toObject(),
      isComplete,
    };
  });

  const { paginatedItems, totalPages, currentPage, totalItems } = paginate(
    updatedTasks,
    15,
    page
  );

  res
    .status(200)
    .json({ tasks: paginatedItems, totalPages, currentPage, totalItems });
});

const getAllTasksbyWallet = asyncHandler(async (req, res) => {
  const { walletAddress, category } = req.query;

  if (!walletAddress) {
    return res.status(400).json({ message: "walletAddress is required" });
  }

  let query = {};

  if (category) {
    query.category = category;
  } else {
    query.category = { $ne: "social" };
  }

  const allTasks = await Task.find(query);

  const updatedTasks = allTasks.map((task) => {
    const taskObj = task.toObject();
    const isComplete = taskObj.completedBy?.some(
      (entry) => entry.walletAddress === walletAddress
    );
    return {
      _id: taskObj._id,
      title: taskObj.title,
      description: taskObj.description,
      requirements: taskObj.requirements,
      points: taskObj.points,
      type: taskObj.type,
      image: taskObj.image,
      url: taskObj.url,
      category: taskObj.category,
      status: taskObj.status,
      date: taskObj.createdAt,
      isComplete,
    };
  });

  res.status(200).json({ tasks: updatedTasks });
});

const getSocialTasksByWallet = asyncHandler(async (req, res) => {
  const { walletAddress } = req.params;

  if (!walletAddress) {
    return res.status(400).json({ message: "walletAddress is required" });
  }

  const socialTasks = await Task.find({ category: "social" });

  const updatedTasks = socialTasks.map((task) => {
    const taskObj = task.toObject();
    const isComplete = taskObj.completedBy?.some(
      (entry) => entry.walletAddress === walletAddress
    );
    return {
      _id: taskObj._id,
      title: taskObj.title,
      description: taskObj.description,
      requirements: taskObj.requirements,
      points: taskObj.points,
      type: taskObj.type,
      image: taskObj.image,
      url: taskObj.url,
      category: taskObj.category,
      status: taskObj.status,
      date: taskObj.createdAt,
      isComplete,
    };
  });

  res.status(200).json({ tasks: updatedTasks });
});

const getAllCategories = asyncHandler(async (req, res) => {
  const categories = await Task.distinct("category");
  res.status(200).json({ categories });
});

const getCategory = asyncHandler(async (req, res) => {
  const categories = await TaskCategory.find();
  res.status(200).json({ categories });
});

const createCategory = asyncHandler(async (req, res) => {
  const { name } = req.body;
  if (!name) {
    return res.status(400).json({ message: "Category name is required" });
  }
  const existingCategory = await TaskCategory.findOne({ name });
  if (existingCategory) {
    return res.status(400).json({ message: "Category already exists" });
  }

  const newCategory = new TaskCategory({ name });
  await newCategory.save();

  res
    .status(201)
    .json({ message: "Category created successfully", category: newCategory });
});

const getTaskById = asyncHandler(async (req, res) => {
  const id = req.params.taskId;
  if (!id) {
    return res.status(400).json({ message: "Missing task id" });
  }
  const task = await Task.findById(id);
  if (!task) {
    return res.status(404).json({ message: "Task not found" });
  }

  res.status(200).json({ task });
});

const updateTask = asyncHandler(async (req, res) => {
  const taskId = req.params.taskId;
  const adminId = req.user?.id;

  if (!adminId) {
    return res
      .status(401)
      .json({ message: "Unauthorized. Admin token missing" });
  }

  const admin = await Admin.findById(adminId);
  if (!admin) {
    return res.status(404).json({ message: "Admin not found" });
  }

  if (!taskId) {
    return res.status(400).json({ message: "Missing task ID" });
  }

  const task = await Task.findById(taskId);
  if (!task) {
    return res.status(404).json({ message: "Task not found" });
  }

  const { title, url, category, points, requirements, description } = req.body;

  if (title) task.title = title;
  if (description) task.description = description;
  if (url) task.url = url;
  if (category) task.category = category;
  if (points) task.points = points;
  if (requirements) task.requirements = requirements;

  if (req.file) {
    try {
      const uploadResult = await uploadFile(req.file, "tasks");

      if (!uploadResult.success) {
        return res.status(400).json({
          message: "Image upload failed",
          error: uploadResult.error,
          details: uploadResult.details,
        });
      }

      const imageUrl =
        uploadResult.data.publicUrl || uploadResult.data.signedUrl;
      task.image = imageUrl;
    } catch (err) {
      return res.status(500).json({
        message: "Image upload failed",
        error: "UPLOAD_ERROR",
        details: err.message,
      });
    }
  }

  await task.save();
  return res.status(200).json({ message: "Task updated successfully", task });
});

const deleteTask = asyncHandler(async (req, res) => {
  const taskId = req.params.taskId;
  const adminId = req.user.id;
  if (!adminId) {
    return res
      .status(401)
      .json({ message: "Unauthorized. Admin token missing" });
  }
  const admin = await Admin.findById(adminId);
  if (!admin) {
    return res.status(404).json({ message: "Admin not found" });
  }
  if (!taskId) {
    return res.status(400).json({ message: "Missing task id" });
  }

  const task = await Task.findById(taskId);
  if (!task) {
    return res.status(404).json({ message: "Task not found" });
  }

  await task.deleteOne(task);
  return res.status(200).json({ message: "Task deleted successfully", task });
});

const performTask = asyncHandler(async (req, res) => {
  const { walletAddress, taskId } = req.params;

  if (!walletAddress || !taskId) {
    return res
      .status(400)
      .json({ message: "Wallet address and Task ID are required" });
  }

  const task = await Task.findById(taskId);
  if (!task) {
    return res.status(404).json({ message: "Task not found" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  });
  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const alreadyCompleted = user.completedTasks.some(
    (q) => q.taskId.toString() === task._id.toString()
  );
  if (alreadyCompleted) {
    return res
      .status(400)
      .json({ message: "User has already completed this task" });
  }

  const existingPoints = await Points.findOne({
    walletAddress:
      user.walletAddress.ethWalletAddress ||
      user.walletAddress.solanaWalletAddress,
    referenceId: task._id,
    pointsType: "task",
    points: 10,
  });

  if (existingPoints) {
    return res
      .status(400)
      .json({ message: "Points already awarded for this task" });
  }

  if (task.isDailyCheckIn) {
    const today = new Date().toDateString();
    const lastLoginDate = user.lastLoginDate
      ? new Date(user.lastLoginDate).toDateString()
      : null;

    if (lastLoginDate === today) {
      return res
        .status(400)
        .json({ message: "Daily check-in already completed today" });
    }

    user.lastLoginDate = new Date();
  }

  user.completedTasks.push({
    taskId: task._id,
    walletAddress:
      user.walletAddress.ethWalletAddress ||
      user.walletAddress.solanaWalletAddress,
    taskPoints: 10,
    taskType: task.type || "task",
  });

  task.completedBy.push({
    walletAddress: user.walletAddress.solanaWalletAddress,
    completedAt: new Date(),
  });

  const pointsRecord = new Points({
    walletAddress: user.walletAddress,
    pointsType: "task",
    points: 10,
    referenceId: task._id,
  });

  await pointsRecord.save();

  user.totalPoints = (user.totalPoints || 0) + 10;

  await Promise.all([user.save(), task.save()]);

  return res.status(200).json({
    message: "Task completed successfully",
    data: {
      completedTasks: user.completedTasks,
      pointsAwarded: 10,
      newTotalPoints: user.totalPoints,
    },
  });
});

const getCompletedTasksByUser = asyncHandler(async (req, res) => {
  const { walletAddress } = req.params;

  if (!walletAddress) {
    return res.status(400).json({ message: "walletAddress is required" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  }).populate("completedTasks.taskId");

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const completedTasks = user.completedTasks
    .filter((entry) => entry.taskId)
    .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))
    .map((entry) => {
      const task = entry.taskId;

      return {
        _id: task._id,
        title: task.title,
        description: task.description,
        requirements: task.requirements,
        points: task.points,
        type: task.type,
        image: task.image,
        url: task.url,
        category: task.category,
        status: task.status,
        isComplete: true,
        completedAt: entry.completedAt,
      };
    });

  res.status(200).json({
    walletAddress,
    totalCompleted: completedTasks.length,
    completedTasks,
  });
});

module.exports = {
  addTask,
  performTask,
  deleteTask,
  getAllTasks,
  getAllTasksbyWallet,
  getTaskById,
  updateTask,
  getAllCategories,
  getCategory,
  createCategory,
  getCompletedTasksByUser,
  getSocialTasksByWallet,
};
